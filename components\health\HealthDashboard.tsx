import React, { useState } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { theme } from '@/constants/theme';
import { HealthPlanCard } from './HealthPlanCard';
import { DailyOverview } from './DailyOverview';
import { HealthSuggestions } from './HealthSuggestions';
import { DateSelector } from './DateSelector';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { PlanNutritionOverview } from './PlanNutritionOverview';

import { Doc } from '@/convex/_generated/dataModel';
import { Text } from '../ui/text';
import { Bot, Grid, Grid2x2, Lightbulb } from 'lucide-react-native';

type Meal = Doc<'meal'>;
type HealthPlan = Doc<'healthPlan'>;

interface HealthDashboardProps {
  activePlan: HealthPlan | undefined | null;
  meals: Meal[] | undefined;
  mealsSummary:
    | {
        totalCalories: number;
        totalProtein: number;
        totalCarbs: number;
        totalFat: number;
        mealsByType: {
          breakfast: number;
          lunch: number;
          dinner: number;
          snacks: number;
        };
      }
    | undefined;
  selectedDate: Date;
  onDateChange: (date: Date) => void;
}

export function HealthDashboard({
  activePlan,
  meals,
  mealsSummary,
  selectedDate,
  onDateChange,
}: HealthDashboardProps) {
  //
  const [tabValue, setTabValue] = useState('overview');

  return (
    <ScrollView
      style={styles.scrollView}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={styles.content}
    >
      <DateSelector selectedDate={selectedDate} onDateChange={onDateChange} />
      <HealthPlanCard activePlan={activePlan || undefined} />
      <Tabs value={tabValue} onValueChange={setTabValue} className="mx-4 mt-4">
        <TabsList
          className="flex-row flex-1"
          style={{ backgroundColor: '#f1f1f1' }}
        >
          <TabsTrigger value="overview" className="flex-1  flex-row gap-3 ">
            <Grid2x2 size={16} />
            <Text>Overview</Text>
          </TabsTrigger>
          <TabsTrigger
            value="recommendations"
            className="  flex-row gap-3 flex-1"
          >
            <Lightbulb size={16} />
            <Text>Suggestions</Text>
          </TabsTrigger>
        </TabsList>
        <TabsContent value="overview">
          <DailyOverview
            mealsSummary={mealsSummary}
            activePlan={activePlan || undefined}
          />
        </TabsContent>
        <TabsContent value="recommendations">
          <HealthSuggestions activePlan={activePlan || undefined} />
        </TabsContent>
      </Tabs>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  scrollView: {
    flex: 1,
  },
  content: {
    paddingBottom: theme.spacing.xl,
  },
});
